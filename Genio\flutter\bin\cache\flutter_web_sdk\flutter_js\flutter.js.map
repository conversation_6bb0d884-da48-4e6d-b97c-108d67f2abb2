{"version": 3, "sources": ["src/browser_environment.js", "src/utils.js", "src/entrypoint_loader.js", "src/service_worker_loader.js", "src/trusted_types.js", "src/instantiate_wasm.js", "src/canvaskit_loader.js", "src/skwasm_loader.js", "src/loader.js", "src/flutter.js"], "sourcesContent": ["// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nconst isBlink = () => {\n  return (navigator.vendor === 'Google Inc.') ||\n    (navigator.agent === 'Edg/');\n}\n\nconst hasImageCodecs = () => {\n  if (typeof ImageDecoder === 'undefined') {\n    return false;\n  }\n  // TODO(yj<PERSON>ov): https://github.com/flutter/flutter/issues/122761\n  // Frequently, when a browser launches an API that other browsers already\n  // support, there are subtle incompatibilities that may cause apps to crash if,\n  // we blindly adopt the new implementation. This check prevents us from picking\n  // up potentially incompatible implementations of ImagdeDecoder API. Instead,\n  // when a new browser engine launches the API, we'll evaluate it and enable it\n  // explicitly.\n  return isBlink();\n}\n\nconst hasChromiumBreakIterators = () => {\n  return (typeof Intl.v8BreakIterator !== \"undefined\") &&\n    (typeof Intl.Segmenter !== \"undefined\");\n}\n\nconst supportsWasmGC = () => {\n  // This attempts to instantiate a wasm module that only will validate if the\n  // final WasmGC spec is implemented in the browser.\n  //\n  // Copied from https://github.com/GoogleChromeLabs/wasm-feature-detect/blob/main/src/detectors/gc/index.js\n  const bytes = [0, 97, 115, 109, 1, 0, 0, 0, 1, 5, 1, 95, 1, 120, 0];\n  return WebAssembly.validate(new Uint8Array(bytes));\n}\n\n/**\n * @returns {import(\"./types\").BrowserEnvironment}\n */\nexport const browserEnvironment = {\n  hasImageCodecs: hasImageCodecs(),\n  hasChromiumBreakIterators: hasChromiumBreakIterators(),\n  supportsWasmGC: supportsWasmGC(),\n  crossOriginIsolated: window.crossOriginIsolated,\n};\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nexport function resolveUrlWithSegments(...segments) {\n  return new URL(joinPathSegments(...segments), document.baseURI).toString()\n}\n\nfunction joinPathSegments(...segments) {\n  return segments.filter((segment) => !!segment).map((segment, i) => {\n    if (i === 0) {\n      return stripRightSlashes(segment);\n    } else {\n      return stripLeftSlashes(stripRightSlashes(segment));\n    }\n  }).filter(x => x.length).join(\"/\")\n}\n\nfunction stripLeftSlashes(s) {\n  let i = 0;\n  while (i < s.length) {\n    if (s.charAt(i) !== \"/\") {\n      break;\n    }\n    i++;\n  }\n  return s.substring(i);\n}\n\nfunction stripRightSlashes(s) {\n  let i = s.length;\n  while (i > 0) {\n    if (s.charAt(i - 1) !== \"/\") {\n      break;\n    }\n    i--;\n  }\n  return s.substring(0, i);\n}\n\n/**\n * Calculates the proper base URL for CanvasKit/Skwasm assets.\n * \n * @param {import(\"./types\").FlutterConfiguration} config\n * @param {import(\"./types\").BuildConfig} buildConfig\n */\nexport function getCanvaskitBaseUrl(config, buildConfig) {\n  if (config.canvasKitBaseUrl) {\n    return config.canvasKitBaseUrl;\n  }\n  if (buildConfig.engineRevision && !buildConfig.useLocalCanvasKit) {\n    return joinPathSegments(\"https://www.gstatic.com/flutter-canvaskit\", buildConfig.engineRevision);\n  }\n  return \"canvaskit\";\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport { resolveUrlWithSegments } from \"./utils.js\";\n\n/**\n * <PERSON>les injecting the main Flutter web entrypoint (main.dart.js), and notifying\n * the user when Flutter is ready, through `didCreateEngineInitializer`.\n *\n * @see https://docs.flutter.dev/development/platform-integration/web/initialization\n */\nexport class FlutterEntrypointLoader {\n  /**\n   * Creates a FlutterEntrypointLoader.\n   */\n  constructor() {\n    // Watchdog to prevent injecting the main entrypoint multiple times.\n    this._scriptLoaded = false;\n  }\n  /**\n   * Injects a TrustedTypesPolicy (or undefined if the feature is not supported).\n   * @param {TrustedTypesPolicy | undefined} policy\n   */\n  setTrustedTypesPolicy(policy) {\n    this._ttPolicy = policy;\n  }\n  /**\n   * @deprecated\n   * Loads flutter main entrypoint, specified by `entrypointUrl`, and calls a\n   * user-specified `onEntrypointLoaded` callback with an EngineInitializer\n   * object when it's done.\n   *\n   * @param {*} options\n   * @returns {Promise | undefined} that will eventually resolve with an\n   * EngineInitializer, or will be rejected with the error caused by the loader.\n   * Returns undefined when an `onEntrypointLoaded` callback is supplied in `options`.\n   */\n  async loadEntrypoint(options) {\n    const { entrypointUrl = resolveUrlWithSegments(\"main.dart.js\"), onEntrypointLoaded, nonce } =\n      options || {};\n    return this._loadJSEntrypoint(entrypointUrl, onEntrypointLoaded, nonce);\n  }\n\n  /**\n   * Loads the entry point for a flutter application.\n   * @param {import(\"./types\").ApplicationBuild} build\n   *   Information about the specific build that is to be loaded\n   * @param {*} deps\n   *   External dependencies that may be needed to load the app.\n   * @param {import(\"./types\").FlutterConfiguration} config\n   *   The application configuration. If no callback is specified, this will be\n   *   passed along to engine when initializing it.\n   * @param {string} nonce\n   *   A nonce to apply to the main application script tag, if necessary.\n   * @param {import(\"./types\").OnEntrypointLoadedCallback?} onEntrypointLoaded\n   *   An optional callback to invoke when the entrypoint is loaded. If no\n   *   callback is supplied, the engine initializer and app runner will be\n   *   automatically invoked on load, passing along the supplied flutter\n   *   configuration.\n   */\n  async load(build, deps, config, nonce, onEntrypointLoaded) {\n    onEntrypointLoaded ??= (engineInitializer) => {\n      engineInitializer.initializeEngine(config).then((appRunner) => appRunner.runApp())\n    };\n    const { entryPointBaseUrl } = config;\n    if (build.compileTarget === \"dart2wasm\") {\n      return this._loadWasmEntrypoint(build, deps, entryPointBaseUrl, onEntrypointLoaded);\n    } else {\n      const mainPath = build.mainJsPath ?? \"main.dart.js\";\n      const entrypointUrl = resolveUrlWithSegments(entryPointBaseUrl, mainPath);\n      return this._loadJSEntrypoint(entrypointUrl, onEntrypointLoaded, nonce);\n    }\n  }\n\n  /**\n   * Resolves the promise created by loadEntrypoint, and calls the `onEntrypointLoaded`\n   * function supplied by the user (if needed).\n   *\n   * Called by Flutter through `_flutter.loader.didCreateEngineInitializer` method,\n   * which is bound to the correct instance of the FlutterEntrypointLoader by\n   * the FlutterLoader object.\n   *\n   * @param {Function} engineInitializer @see https://github.com/flutter/engine/blob/main/lib/web_ui/lib/src/engine/js_interop/js_loader.dart#L42\n   */\n  didCreateEngineInitializer(engineInitializer) {\n    if (typeof this._didCreateEngineInitializerResolve === \"function\") {\n      this._didCreateEngineInitializerResolve(engineInitializer);\n      // Remove the resolver after the first time, so Flutter Web can hot restart.\n      this._didCreateEngineInitializerResolve = null;\n      // Make the engine revert to \"auto\" initialization on hot restart.\n      delete _flutter.loader.didCreateEngineInitializer;\n    }\n    if (typeof this._onEntrypointLoaded === \"function\") {\n      this._onEntrypointLoaded(engineInitializer);\n    }\n  }\n  /**\n   * Injects a script tag into the DOM, and configures this loader to be able to\n   * handle the \"entrypoint loaded\" notifications received from Flutter web.\n   *\n   * @param {string} entrypointUrl the URL of the script that will initialize\n   *                 Flutter.\n   * @param {Function} onEntrypointLoaded a callback that will be called when\n   *                   Flutter web notifies this object that the entrypoint is\n   *                   loaded.\n   * @returns {Promise | undefined} a Promise that resolves when the entrypoint\n   *                                is loaded, or undefined if `onEntrypointLoaded`\n   *                                is a function.\n   */\n  _loadJSEntrypoint(entrypointUrl, onEntrypointLoaded, nonce) {\n    const useCallback = typeof onEntrypointLoaded === \"function\";\n    if (!this._scriptLoaded) {\n      this._scriptLoaded = true;\n      const scriptTag = this._createScriptTag(entrypointUrl, nonce);\n      if (useCallback) {\n        // Just inject the script tag, and return nothing; Flutter will call\n        // `didCreateEngineInitializer` when it's done.\n        console.debug(\"Injecting <script> tag. Using callback.\");\n        this._onEntrypointLoaded = onEntrypointLoaded;\n        document.head.append(scriptTag);\n      } else {\n        // Inject the script tag and return a promise that will get resolved\n        // with the EngineInitializer object from Flutter when it calls\n        // `didCreateEngineInitializer` later.\n        return new Promise((resolve, reject) => {\n          console.debug(\n            \"Injecting <script> tag. Using Promises. Use the callback approach instead!\"\n          );\n          this._didCreateEngineInitializerResolve = resolve;\n          scriptTag.addEventListener(\"error\", reject);\n          document.head.append(scriptTag);\n        });\n      }\n    }\n  }\n\n  /**\n   *\n   * @param {import(\"./types\").WasmApplicationBuild} build\n   * @param {*} deps\n   * @param {string} entryPointBaseUrl\n   * @param {import(\"./types\").OnEntrypointLoadedCallback} onEntrypointLoaded\n   */\n  async _loadWasmEntrypoint(build, deps, entrypointBaseUrl, onEntrypointLoaded) {\n    if (!this._scriptLoaded) {\n      this._scriptLoaded = true;\n\n      this._onEntrypointLoaded = onEntrypointLoaded;\n      const { mainWasmPath, jsSupportRuntimePath } = build;\n      const moduleUri = resolveUrlWithSegments(entrypointBaseUrl, mainWasmPath);\n      let jsSupportRuntimeUri = resolveUrlWithSegments(entrypointBaseUrl, jsSupportRuntimePath);\n      if (this._ttPolicy != null) {\n        jsSupportRuntimeUri = this._ttPolicy.createScriptURL(jsSupportRuntimeUri);\n      }\n      const jsSupportRuntime = await import(jsSupportRuntimeUri);\n\n      const compiledDartAppPromise = jsSupportRuntime.compileStreaming(fetch(moduleUri));\n\n      let importsPromise;\n      if (build.renderer === \"skwasm\") {\n        importsPromise = (async () => {\n          const skwasmInstance = await deps.skwasm;\n          window._flutter_skwasmInstance = skwasmInstance;\n          return {\n            skwasm: skwasmInstance.wasmExports,\n            skwasmWrapper: skwasmInstance,\n            ffi: {\n              memory: skwasmInstance.wasmMemory,\n            },\n          };\n        })();\n      } else {\n        importsPromise = Promise.resolve({});\n      }\n      const compiledDartApp = await compiledDartAppPromise;\n      const dartApp = await compiledDartApp.instantiate(await importsPromise);\n      await dartApp.invokeMain();\n    }\n  }\n\n  /**\n   * Creates a script tag for the given URL.\n   * @param {string} url\n   * @returns {HTMLScriptElement}\n   */\n  _createScriptTag(url, nonce) {\n    const scriptTag = document.createElement(\"script\");\n    scriptTag.type = \"application/javascript\";\n    if (nonce) {\n      scriptTag.nonce = nonce;\n    }\n    // Apply TrustedTypes validation, if available.\n    let trustedUrl = url;\n    if (this._ttPolicy != null) {\n      trustedUrl = this._ttPolicy.createScriptURL(url);\n    }\n    scriptTag.src = trustedUrl;\n    return scriptTag;\n  }\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport { resolveUrlWithSegments } from \"./utils.js\";\n\n/**\n * Wraps `promise` in a timeout of the given `duration` in ms.\n *\n * Resolves/rejects with whatever the original `promises` does, or rejects\n * if `promise` takes longer to complete than `duration`. In that case,\n * `debugName` is used to compose a legible error message.\n *\n * If `duration` is < 0, the original `promise` is returned unchanged.\n * @param {Promise} promise\n * @param {number} duration\n * @param {string} debugName\n * @returns {Promise} a wrapped promise.\n */\nasync function timeout(promise, duration, debugName) {\n  if (duration < 0) {\n    return promise;\n  }\n  let timeoutId;\n  const _clock = new Promise((_, reject) => {\n    timeoutId = setTimeout(() => {\n      reject(\n        new Error(\n          `${debugName} took more than ${duration}ms to resolve. Moving on.`,\n          {\n            cause: timeout,\n          }\n        )\n      );\n    }, duration);\n  });\n  return Promise.race([promise, _clock]).finally(() => {\n    clearTimeout(timeoutId);\n  });\n}\n\n/**\n * Handles loading/reloading <PERSON>lut<PERSON>'s service worker, if configured.\n *\n * @see: https://developers.google.com/web/fundamentals/primers/service-workers\n */\nexport class FlutterServiceWorkerLoader {\n  /**\n   * Injects a TrustedTypesPolicy (or undefined if the feature is not supported).\n   * @param {TrustedTypesPolicy | undefined} policy\n   */\n  setTrustedTypesPolicy(policy) {\n    this._ttPolicy = policy;\n  }\n  /**\n   * Returns a Promise that resolves when the latest Flutter service worker,\n   * configured by `settings` has been loaded and activated.\n   *\n   * Otherwise, the promise is rejected with an error message.\n   * @param {import(\"./types\").ServiceWorkerSettings} settings Service worker settings\n   * @returns {Promise} that resolves when the latest serviceWorker is ready.\n   */\n  loadServiceWorker(settings) {\n    if (!settings) {\n      // In the future, settings = null -> uninstall service worker?\n      console.debug(\"Null serviceWorker configuration. Skipping.\");\n      return Promise.resolve();\n    }\n    if (!(\"serviceWorker\" in navigator)) {\n      let errorMessage = \"Service Worker API unavailable.\";\n      if (!window.isSecureContext) {\n        errorMessage += \"\\nThe current context is NOT secure.\"\n        errorMessage += \"\\nRead more: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\";\n      }\n      return Promise.reject(\n        new Error(errorMessage)\n      );\n    }\n    const {\n      serviceWorkerVersion,\n      serviceWorkerUrl = resolveUrlWithSegments(`flutter_service_worker.js?v=${serviceWorkerVersion}`),\n      timeoutMillis = 4000,\n    } = settings;\n    // Apply the TrustedTypes policy, if present.\n    let url = serviceWorkerUrl;\n    if (this._ttPolicy != null) {\n      url = this._ttPolicy.createScriptURL(url);\n    }\n    const serviceWorkerActivation = navigator.serviceWorker\n      .register(url)\n      .then((serviceWorkerRegistration) => this._getNewServiceWorker(serviceWorkerRegistration, serviceWorkerVersion))\n      .then(this._waitForServiceWorkerActivation);\n    // Timeout race promise\n    return timeout(\n      serviceWorkerActivation,\n      timeoutMillis,\n      \"prepareServiceWorker\"\n    );\n  }\n  /**\n   * Returns the latest service worker for the given `serviceWorkerRegistration`.\n   *\n   * This might return the current service worker, if there's no new service worker\n   * awaiting to be installed/updated.\n   *\n   * @param {ServiceWorkerRegistration} serviceWorkerRegistration\n   * @param {string} serviceWorkerVersion\n   * @returns {Promise<ServiceWorker>}\n   */\n  async _getNewServiceWorker(serviceWorkerRegistration, serviceWorkerVersion) {\n    if (!serviceWorkerRegistration.active && (serviceWorkerRegistration.installing || serviceWorkerRegistration.waiting)) {\n      // No active web worker and we have installed or are installing\n      // one for the first time. Simply wait for it to activate.\n      console.debug(\"Installing/Activating first service worker.\");\n      return serviceWorkerRegistration.installing || serviceWorkerRegistration.waiting;\n    } else if (!serviceWorkerRegistration.active.scriptURL.endsWith(serviceWorkerVersion)) {\n      // When the app updates the serviceWorkerVersion changes, so we\n      // need to ask the service worker to update.\n      const newRegistration = await serviceWorkerRegistration.update();\n      console.debug(\"Updating service worker.\");\n      return newRegistration.installing || newRegistration.waiting || newRegistration.active;\n    } else {\n      console.debug(\"Loading from existing service worker.\");\n      return serviceWorkerRegistration.active;\n    }\n  }\n  /**\n   * Returns a Promise that resolves when the `serviceWorker` changes its\n   * state to \"activated\".\n   *\n   * @param {ServiceWorker} serviceWorker\n   * @returns {Promise<void>}\n   */\n  async _waitForServiceWorkerActivation(serviceWorker) {\n    if (!serviceWorker || serviceWorker.state === \"activated\") {\n      if (!serviceWorker) {\n        throw new Error(\"Cannot activate a null service worker!\");\n      } else {\n        console.debug(\"Service worker already active.\");\n        return;\n      }\n    }\n    return new Promise((resolve, _) => {\n      serviceWorker.addEventListener(\"statechange\", () => {\n        if (serviceWorker.state === \"activated\") {\n          console.debug(\"Activated new service worker.\");\n          resolve();\n        }\n      });\n    });\n  }\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Handles the creation of a TrustedTypes `policy` that validates URLs based\n * on an (optional) incoming array of RegExes.\n */\nexport class FlutterTrustedTypesPolicy {\n  /**\n   * Constructs the policy.\n   * @param {[RegExp]} validPatterns the patterns to test URLs\n   * @param {String} policyName the policy name (optional)\n   */\n  constructor(validPatterns, policyName = \"flutter-js\") {\n    const patterns = validPatterns || [\n      /\\.js$/,\n      /\\.mjs$/,\n    ];\n    if (window.trustedTypes) {\n      this.policy = trustedTypes.createPolicy(policyName, {\n        createScriptURL: function (url) {\n          // Return blob urls without manipulating them\n          if (url.startsWith('blob:')) {\n            return url;\n          }\n          // Validate other urls\n          const parsed = new URL(url, window.location);\n          const file = parsed.pathname.split(\"/\").pop();\n          const matches = patterns.some((pattern) => pattern.test(file));\n          if (matches) {\n            return parsed.toString();\n          }\n          console.error(\n            \"URL rejected by TrustedTypes policy\",\n            policyName, \":\", url, \"(download prevented)\");\n        }\n      });\n    }\n  }\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n// This is a little helper function that helps us start the fetch and compilation\n// of an emscripten wasm module in parallel with the fetch of its script.\nexport const createWasmInstantiator = (url) => {\n  const modulePromise = WebAssembly.compileStreaming(fetch(url));\n  return (imports, successCallback) => {\n    (async () => {\n      const module = await modulePromise;\n      const instance = await WebAssembly.instantiate(module, imports);\n      successCallback(instance, module);\n    })();\n    return {};\n  };\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport { createWasmInstantiator } from \"./instantiate_wasm.js\";\nimport { resolveUrlWithSegments } from \"./utils.js\";\n\nexport const loadCanvasKit = (deps, config, browserEnvironment, canvasKitBaseUrl) => {\n  window.flutterCanvasKitLoaded = (async () => {\n    if (window.flutterCanvasKit) {\n      // The user has set this global variable ahead of time, so we just return that.\n      return window.flutterCanvasKit;\n    }\n    const supportsChromiumCanvasKit = browserEnvironment.hasChromiumBreakIterators && browserEnvironment.hasImageCodecs;\n    if (!supportsChromiumCanvasKit && config.canvasKitVariant == \"chromium\") {\n      throw \"Chromium CanvasKit variant specifically requested, but unsupported in this browser\";\n    }\n    const useChromiumCanvasKit = supportsChromiumCanvasKit && (config.canvasKitVariant !== \"full\");\n    let baseUrl = canvasKitBaseUrl;\n    if (useChromiumCanvasKit) {\n      baseUrl = resolveUrlWithSegments(baseUrl, \"chromium\");\n    }\n    let canvasKitUrl = resolveUrlWithSegments(baseUrl, \"canvaskit.js\");\n    if (deps.flutterTT.policy) {\n      canvasKitUrl = deps.flutterTT.policy.createScriptURL(canvasKitUrl);\n    }\n    const wasmInstantiator = createWasmInstantiator(resolveUrlWithSegments(baseUrl, \"canvaskit.wasm\"));\n    const canvasKitModule = await import(canvasKitUrl);\n    window.flutterCanvasKit = await canvasKitModule.default({\n      instantiateWasm: wasmInstantiator,\n    });\n    return window.flutterCanvasKit;\n  })();\n  return window.flutterCanvasKitLoaded;\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport { createWasmInstantiator } from \"./instantiate_wasm.js\";\nimport { resolveUrlWithSegments } from \"./utils.js\";\n\nexport const loadSkwasm = async (deps, config, browserEnvironment, baseUrl) => {\n  const rawSkwasmUrl = resolveUrlWithSegments(baseUrl, 'skwasm.js')\n  let skwasmUrl = rawSkwasmUrl;\n  if (deps.flutterTT.policy) {\n    skwasmUrl = deps.flutterTT.policy.createScriptURL(skwasmUrl);\n  }\n  const wasmInstantiator = createWasmInstantiator(resolveUrlWithSegments(baseUrl, 'skwasm.wasm'));\n  const skwasm = await import(skwasmUrl);\n  return await skwasm.default({\n    skwasmSingleThreaded: !browserEnvironment.crossOriginIsolated || config.forceSingleThreadedSkwasm,\n    instantiateWasm: wasmInstantiator,\n    locateFile: (filename, scriptDirectory) => {\n      // The wasm workers API has a separate .ww.js file that bootstraps the\n      // web worker. However, it turns out this worker bootstrapper doesn't\n      // actually work with ES6 modules, which we have enabled. So we instead\n      // pass our own bootstrapper that loads skwasm.js as an ES6 module, and\n      // queues/flushes pending messages that were received during the\n      // asynchronous load.\n      if (filename.endsWith('.ww.js')) {\n        const url = resolveUrlWithSegments(baseUrl, filename);\n        return URL.createObjectURL(new Blob(\n          [`\n\"use strict\";\n\nlet eventListener;\neventListener = (message) => {\n    const pendingMessages = [];\n    const data = message.data;\n    data[\"instantiateWasm\"] = (info,receiveInstance) => {\n        const instance = new WebAssembly.Instance(data[\"wasm\"], info);\n        return receiveInstance(instance, data[\"wasm\"])\n    };\n    import(data.js).then(async (skwasm) => {\n        await skwasm.default(data);\n\n        removeEventListener(\"message\", eventListener);\n        for (const message of pendingMessages) {\n            dispatchEvent(message);\n        }\n    });\n    removeEventListener(\"message\", eventListener);\n    eventListener = (message) => {\n\n        pendingMessages.push(message);\n    };\n\n    addEventListener(\"message\", eventListener);\n};\naddEventListener(\"message\", eventListener);\n`\n          ],\n          { 'type': 'application/javascript' }));\n      }\n      return url;\n    },\n    // Because of the above workaround, the worker is just a blob and\n    // can't locate the main script using a relative path to itself,\n    // so we pass the main script location in.\n    mainScriptUrlOrBlob: rawSkwasmUrl,\n  });\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport { browserEnvironment } from './browser_environment.js';\nimport { FlutterEntrypointLoader } from './entrypoint_loader.js';\nimport { FlutterServiceWorkerLoader } from './service_worker_loader.js';\nimport { FlutterTrustedTypesPolicy } from './trusted_types.js';\nimport { loadCanvasKit } from './canvaskit_loader.js';\nimport { loadSkwasm } from './skwasm_loader.js';\nimport { getCanvaskitBaseUrl } from './utils.js';\n\n/**\n * The public interface of _flutter.loader. Exposes two methods:\n * * loadEntrypoint (which coordinates the default Flutter web loading procedure)\n * * didCreateEngineInitializer (which is called by Flutter to notify that its\n *                              Engine is ready to be initialized)\n */\nexport class FlutterLoader {\n  /**\n   * @deprecated Use `load` instead.\n   * Initializes the Flutter web app.\n   * @param {*} options\n   * @returns {Promise?} a (Deprecated) Promise that will eventually resolve\n   *                     with an EngineInitializer, or will be rejected with\n   *                     any error caused by the loader. Or Null, if the user\n   *                     supplies an `onEntrypointLoaded` Function as an option.\n   */\n  async loadEntrypoint(options) {\n    const { serviceWorker, ...entrypoint } = options || {};\n    // A Trusted Types policy that is going to be used by the loader.\n    const flutterTT = new FlutterTrustedTypesPolicy();\n    // The FlutterServiceWorkerLoader instance could be injected as a dependency\n    // (and dynamically imported from a module if not present).\n    const serviceWorkerLoader = new FlutterServiceWorkerLoader();\n    serviceWorkerLoader.setTrustedTypesPolicy(flutterTT.policy);\n    await serviceWorkerLoader.loadServiceWorker(serviceWorker).catch(e => {\n      // Regardless of what happens with the injection of the SW, the show must go on\n      console.warn(\"Exception while loading service worker:\", e);\n    });\n    // The FlutterEntrypointLoader instance could be injected as a dependency\n    // (and dynamically imported from a module if not present).\n    const entrypointLoader = new FlutterEntrypointLoader();\n    entrypointLoader.setTrustedTypesPolicy(flutterTT.policy);\n    // Install the `didCreateEngineInitializer` listener where Flutter web expects it to be.\n    this.didCreateEngineInitializer =\n      entrypointLoader.didCreateEngineInitializer.bind(entrypointLoader);\n    return entrypointLoader.loadEntrypoint(entrypoint);\n  }\n\n  /**\n   * Loads and initializes a flutter application.\n   * @param {Object} options\n   * @param {import(\"/.types\".ServiceWorkerSettings?)} options.serviceWorkerSettings\n   *   Settings for the service worker to be loaded. Can pass `undefined` or\n   *   `null` to not launch a service worker at all.\n   * @param {import(\"/.types\".OnEntryPointLoadedCallback)} options.onEntrypointLoaded\n   *   An optional callback to invoke \n   * @param {string} options.nonce\n   *   A nonce to be applied to the main JS script when loading it, which may\n   *   be required by the sites Content-Security-Policy.\n   *   For more details, see {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/script-src here}.\n   * @param {import(\"./types\".FlutterConfiguration)} arg.config\n   */\n  async load({\n    serviceWorkerSettings,\n    onEntrypointLoaded,\n    nonce,\n    config,\n  } = {}) {\n    config ??= {};\n\n    /** @type {import(\"./types\").BuildConfig} */\n    const buildConfig = _flutter.buildConfig;\n    if (!buildConfig) {\n      throw \"FlutterLoader.load requires _flutter.buildConfig to be set\";\n    }\n\n    const rendererIsCompatible = (renderer) => {\n      switch (renderer) {\n        case \"skwasm\":\n          return browserEnvironment.hasChromiumBreakIterators\n            && browserEnvironment.hasImageCodecs\n            && browserEnvironment.supportsWasmGC;\n        default:\n          return true;\n      }\n    }\n\n    /**\n     * @param {import(\"./types\").ApplicationBuild} build\n     * @param {import(\"./types\").WebRenderer} renderer\n     **/\n    const buildContainsRenderer = (build, renderer) => {\n      return build.renderer == renderer;\n    }\n\n    const buildIsCompatible = (build) => {\n      if (build.compileTarget === \"dart2wasm\" && !browserEnvironment.supportsWasmGC) {\n        return false;\n      }\n      if (config.renderer && !buildContainsRenderer(build, config.renderer)) {\n        return false;\n      }\n      return rendererIsCompatible(build.renderer);\n    };\n    const build = buildConfig.builds.find(buildIsCompatible);\n    if (!build) {\n      throw \"FlutterLoader could not find a build compatible with configuration and environment.\";\n    }\n\n    const deps = {};\n    deps.flutterTT = new FlutterTrustedTypesPolicy();\n    if (serviceWorkerSettings) {\n      deps.serviceWorkerLoader = new FlutterServiceWorkerLoader();\n      deps.serviceWorkerLoader.setTrustedTypesPolicy(deps.flutterTT.policy);\n      await deps.serviceWorkerLoader.loadServiceWorker(serviceWorkerSettings).catch(e => {\n        // Regardless of what happens with the injection of the SW, the show must go on\n        console.warn(\"Exception while loading service worker:\", e);\n      });\n    }\n\n    const canvasKitBaseUrl = getCanvaskitBaseUrl(config, buildConfig);\n    if (build.renderer === \"canvaskit\") {\n      deps.canvasKit = loadCanvasKit(deps, config, browserEnvironment, canvasKitBaseUrl);\n    } else if (build.renderer === \"skwasm\") {\n      deps.skwasm = loadSkwasm(deps, config, browserEnvironment, canvasKitBaseUrl);\n    }\n\n    // The FlutterEntrypointLoader instance could be injected as a dependency\n    // (and dynamically imported from a module if not present).\n    const entrypointLoader = new FlutterEntrypointLoader();\n    entrypointLoader.setTrustedTypesPolicy(deps.flutterTT.policy);\n    // Install the `didCreateEngineInitializer` listener where Flutter web expects it to be.\n    this.didCreateEngineInitializer =\n      entrypointLoader.didCreateEngineInitializer.bind(entrypointLoader);\n    return entrypointLoader.load(build, deps, config, nonce, onEntrypointLoaded);\n  }\n}\n", "// Copyright 2013 The Flutter Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport { FlutterLoader } from './loader.js';\n\nif (!window._flutter) {\n  window._flutter = {};\n}\n\nif (!window._flutter.loader) {\n  window._flutter.loader = new FlutterLoader();\n}\n"], "mappings": "MAIA,IAAMA,EAAU,IACN,UAAU,SAAW,eAC1B,UAAU,QAAU,OAGnBC,EAAiB,IACjB,OAAO,aAAiB,IACnB,GASFD,EAAQ,EAGXE,EAA4B,IACxB,OAAO,KAAK,gBAAoB,KACrC,OAAO,KAAK,UAAc,IAGzBC,EAAiB,IAAM,CAK3B,IAAMC,EAAQ,CAAC,EAAG,GAAI,IAAK,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,IAAK,CAAC,EAClE,OAAO,YAAY,SAAS,IAAI,WAAWA,CAAK,CAAC,CACnD,EAKaC,EAAqB,CAChC,eAAgBJ,EAAe,EAC/B,0BAA2BC,EAA0B,EACrD,eAAgBC,EAAe,EAC/B,oBAAqB,OAAO,mBAC9B,ECzCO,SAASG,KAA0BC,EAAU,CAClD,OAAO,IAAI,IAAIC,EAAiB,GAAGD,CAAQ,EAAG,SAAS,OAAO,EAAE,SAAS,CAC3E,CAEA,SAASC,KAAoBD,EAAU,CACrC,OAAOA,EAAS,OAAQE,GAAY,CAAC,CAACA,CAAO,EAAE,IAAI,CAACA,EAAS,IACvD,IAAM,EACDC,EAAkBD,CAAO,EAEzBE,EAAiBD,EAAkBD,CAAO,CAAC,CAErD,EAAE,OAAOG,GAAKA,EAAE,MAAM,EAAE,KAAK,GAAG,CACnC,CAEA,SAASD,EAAiB,EAAG,CAC3B,IAAIE,EAAI,EACR,KAAOA,EAAI,EAAE,QACP,EAAE,OAAOA,CAAC,IAAM,KAGpBA,IAEF,OAAO,EAAE,UAAUA,CAAC,CACtB,CAEA,SAASH,EAAkB,EAAG,CAC5B,IAAIG,EAAI,EAAE,OACV,KAAOA,EAAI,GACL,EAAE,OAAOA,EAAI,CAAC,IAAM,KAGxBA,IAEF,OAAO,EAAE,UAAU,EAAGA,CAAC,CACzB,CAQO,SAASC,EAAoBC,EAAQC,EAAa,CACvD,OAAID,EAAO,iBACFA,EAAO,iBAEZC,EAAY,gBAAkB,CAACA,EAAY,kBACtCR,EAAiB,4CAA6CQ,EAAY,cAAc,EAE1F,WACT,CC1CO,IAAMC,EAAN,KAA8B,CAInC,aAAc,CAEZ,KAAK,cAAgB,EACvB,CAKA,sBAAsBC,EAAQ,CAC5B,KAAK,UAAYA,CACnB,CAYA,MAAM,eAAeC,EAAS,CAC5B,GAAM,CAAE,cAAAC,EAAgBC,EAAuB,cAAc,EAAG,mBAAAC,EAAoB,MAAAC,CAAM,EACxFJ,GAAW,CAAC,EACd,OAAO,KAAK,kBAAkBC,EAAeE,EAAoBC,CAAK,CACxE,CAmBA,MAAM,KAAKC,EAAOC,EAAMC,EAAQH,EAAOD,EAAoB,CACzDA,IAAwBK,GAAsB,CAC5CA,EAAkB,iBAAiBD,CAAM,EAAE,KAAME,GAAcA,EAAU,OAAO,CAAC,CACnF,EACA,GAAM,CAAE,kBAAAC,CAAkB,EAAIH,EAC9B,GAAIF,EAAM,gBAAkB,YAC1B,OAAO,KAAK,oBAAoBA,EAAOC,EAAMI,EAAmBP,CAAkB,EAC7E,CACL,IAAMQ,EAAWN,EAAM,YAAc,eAC/BJ,EAAgBC,EAAuBQ,EAAmBC,CAAQ,EACxE,OAAO,KAAK,kBAAkBV,EAAeE,EAAoBC,CAAK,CACxE,CACF,CAYA,2BAA2BI,EAAmB,CACxC,OAAO,KAAK,oCAAuC,aACrD,KAAK,mCAAmCA,CAAiB,EAEzD,KAAK,mCAAqC,KAE1C,OAAO,SAAS,OAAO,4BAErB,OAAO,KAAK,qBAAwB,YACtC,KAAK,oBAAoBA,CAAiB,CAE9C,CAcA,kBAAkBP,EAAeE,EAAoBC,EAAO,CAC1D,IAAMQ,EAAc,OAAOT,GAAuB,WAClD,GAAI,CAAC,KAAK,cAAe,CACvB,KAAK,cAAgB,GACrB,IAAMU,EAAY,KAAK,iBAAiBZ,EAAeG,CAAK,EAC5D,GAAIQ,EAGF,QAAQ,MAAM,yCAAyC,EACvD,KAAK,oBAAsBT,EAC3B,SAAS,KAAK,OAAOU,CAAS,MAK9B,QAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CACtC,QAAQ,MACN,4EACF,EACA,KAAK,mCAAqCD,EAC1CD,EAAU,iBAAiB,QAASE,CAAM,EAC1C,SAAS,KAAK,OAAOF,CAAS,CAChC,CAAC,CAEL,CACF,CASA,MAAM,oBAAoBR,EAAOC,EAAMU,EAAmBb,EAAoB,CAC5E,GAAI,CAAC,KAAK,cAAe,CACvB,KAAK,cAAgB,GAErB,KAAK,oBAAsBA,EAC3B,GAAM,CAAE,aAAAc,EAAc,qBAAAC,CAAqB,EAAIb,EACzCc,EAAYjB,EAAuBc,EAAmBC,CAAY,EACpEG,EAAsBlB,EAAuBc,EAAmBE,CAAoB,EACpF,KAAK,WAAa,OACpBE,EAAsB,KAAK,UAAU,gBAAgBA,CAAmB,GAI1E,IAAMC,GAFmB,MAAM,OAAOD,IAEU,iBAAiB,MAAMD,CAAS,CAAC,EAE7EG,EACAjB,EAAM,WAAa,SACrBiB,GAAkB,SAAY,CAC5B,IAAMC,EAAiB,MAAMjB,EAAK,OAClC,cAAO,wBAA0BiB,EAC1B,CACL,OAAQA,EAAe,YACvB,cAAeA,EACf,IAAK,CACH,OAAQA,EAAe,UACzB,CACF,CACF,GAAG,EAEHD,EAAiB,QAAQ,QAAQ,CAAC,CAAC,EAIrC,MADgB,MADQ,MAAMD,GACQ,YAAY,MAAMC,CAAc,GACxD,WAAW,CAC3B,CACF,CAOA,iBAAiBE,EAAKpB,EAAO,CAC3B,IAAMS,EAAY,SAAS,cAAc,QAAQ,EACjDA,EAAU,KAAO,yBACbT,IACFS,EAAU,MAAQT,GAGpB,IAAIqB,EAAaD,EACjB,OAAI,KAAK,WAAa,OACpBC,EAAa,KAAK,UAAU,gBAAgBD,CAAG,GAEjDX,EAAU,IAAMY,EACTZ,CACT,CACF,ECrLA,eAAea,EAAQC,EAASC,EAAUC,EAAW,CACnD,GAAID,EAAW,EACb,OAAOD,EAET,IAAIG,EACEC,EAAS,IAAI,QAAQ,CAACC,EAAGC,IAAW,CACxCH,EAAY,WAAW,IAAM,CAC3BG,EACE,IAAI,MACF,GAAGJ,CAAS,mBAAmBD,CAAQ,4BACvC,CACE,MAAOF,CACT,CACF,CACF,CACF,EAAGE,CAAQ,CACb,CAAC,EACD,OAAO,QAAQ,KAAK,CAACD,EAASI,CAAM,CAAC,EAAE,QAAQ,IAAM,CACnD,aAAaD,CAAS,CACxB,CAAC,CACH,CAOO,IAAMI,EAAN,KAAiC,CAKtC,sBAAsBC,EAAQ,CAC5B,KAAK,UAAYA,CACnB,CASA,kBAAkBC,EAAU,CAC1B,GAAI,CAACA,EAEH,eAAQ,MAAM,6CAA6C,EACpD,QAAQ,QAAQ,EAEzB,GAAI,EAAE,kBAAmB,WAAY,CACnC,IAAIC,EAAe,kCACnB,OAAK,OAAO,kBACVA,GAAgB;AAAA,oCAChBA,GAAgB;AAAA,mFAEX,QAAQ,OACb,IAAI,MAAMA,CAAY,CACxB,CACF,CACA,GAAM,CACJ,qBAAAC,EACA,iBAAAC,EAAmBC,EAAuB,+BAA+BF,CAAoB,EAAE,EAC/F,cAAAG,EAAgB,GAClB,EAAIL,EAEAM,EAAMH,EACN,KAAK,WAAa,OACpBG,EAAM,KAAK,UAAU,gBAAgBA,CAAG,GAE1C,IAAMC,EAA0B,UAAU,cACvC,SAASD,CAAG,EACZ,KAAME,GAA8B,KAAK,qBAAqBA,EAA2BN,CAAoB,CAAC,EAC9G,KAAK,KAAK,+BAA+B,EAE5C,OAAOZ,EACLiB,EACAF,EACA,sBACF,CACF,CAWA,MAAM,qBAAqBG,EAA2BN,EAAsB,CAC1E,GAAI,CAACM,EAA0B,SAAWA,EAA0B,YAAcA,EAA0B,SAG1G,eAAQ,MAAM,6CAA6C,EACpDA,EAA0B,YAAcA,EAA0B,QACpE,GAAKA,EAA0B,OAAO,UAAU,SAASN,CAAoB,EAOlF,eAAQ,MAAM,uCAAuC,EAC9CM,EAA0B,OARoD,CAGrF,IAAMC,EAAkB,MAAMD,EAA0B,OAAO,EAC/D,eAAQ,MAAM,0BAA0B,EACjCC,EAAgB,YAAcA,EAAgB,SAAWA,EAAgB,MAClF,CAIF,CAQA,MAAM,gCAAgCC,EAAe,CACnD,GAAI,CAACA,GAAiBA,EAAc,QAAU,YAC5C,GAAKA,EAEE,CACL,QAAQ,MAAM,gCAAgC,EAC9C,MACF,KAJE,OAAM,IAAI,MAAM,wCAAwC,EAM5D,OAAO,IAAI,QAAQ,CAACC,EAASf,IAAM,CACjCc,EAAc,iBAAiB,cAAe,IAAM,CAC9CA,EAAc,QAAU,cAC1B,QAAQ,MAAM,+BAA+B,EAC7CC,EAAQ,EAEZ,CAAC,CACH,CAAC,CACH,CACF,EC/IO,IAAMC,EAAN,KAAgC,CAMrC,YAAYC,EAAeC,EAAa,aAAc,CACpD,IAAMC,EAAWF,GAAiB,CAChC,QACA,QACF,EACI,OAAO,eACT,KAAK,OAAS,aAAa,aAAaC,EAAY,CAClD,gBAAiB,SAAUE,EAAK,CAE9B,GAAIA,EAAI,WAAW,OAAO,EACxB,OAAOA,EAGT,IAAMC,EAAS,IAAI,IAAID,EAAK,OAAO,QAAQ,EACrCE,EAAOD,EAAO,SAAS,MAAM,GAAG,EAAE,IAAI,EAE5C,GADgBF,EAAS,KAAMI,GAAYA,EAAQ,KAAKD,CAAI,CAAC,EAE3D,OAAOD,EAAO,SAAS,EAEzB,QAAQ,MACN,sCACAH,EAAY,IAAKE,EAAK,sBAAsB,CAChD,CACF,CAAC,EAEL,CACF,EClCO,IAAMI,EAA0BC,GAAQ,CAC7C,IAAMC,EAAgB,YAAY,iBAAiB,MAAMD,CAAG,CAAC,EAC7D,MAAO,CAACE,EAASC,MACd,SAAY,CACX,IAAMC,EAAS,MAAMH,EACfI,EAAW,MAAM,YAAY,YAAYD,EAAQF,CAAO,EAC9DC,EAAgBE,EAAUD,CAAM,CAClC,GAAG,EACI,CAAC,EAEZ,ECTO,IAAME,EAAgB,CAACC,EAAMC,EAAQC,EAAoBC,KAC9D,OAAO,wBAA0B,SAAY,CAC3C,GAAI,OAAO,iBAET,OAAO,OAAO,iBAEhB,IAAMC,EAA4BF,EAAmB,2BAA6BA,EAAmB,eACrG,GAAI,CAACE,GAA6BH,EAAO,kBAAoB,WAC3D,KAAM,qFAER,IAAMI,EAAuBD,GAA8BH,EAAO,mBAAqB,OACnFK,EAAUH,EACVE,IACFC,EAAUC,EAAuBD,EAAS,UAAU,GAEtD,IAAIE,EAAeD,EAAuBD,EAAS,cAAc,EAC7DN,EAAK,UAAU,SACjBQ,EAAeR,EAAK,UAAU,OAAO,gBAAgBQ,CAAY,GAEnE,IAAMC,EAAmBC,EAAuBH,EAAuBD,EAAS,gBAAgB,CAAC,EAC3FK,EAAkB,MAAM,OAAOH,GACrC,cAAO,iBAAmB,MAAMG,EAAgB,QAAQ,CACtD,gBAAiBF,CACnB,CAAC,EACM,OAAO,gBAChB,GAAG,EACI,OAAO,wBC1BT,IAAMG,EAAa,MAAOC,EAAMC,EAAQC,EAAoBC,IAAY,CAC7E,IAAMC,EAAeC,EAAuBF,EAAS,WAAW,EAC5DG,EAAYF,EACZJ,EAAK,UAAU,SACjBM,EAAYN,EAAK,UAAU,OAAO,gBAAgBM,CAAS,GAE7D,IAAMC,EAAmBC,EAAuBH,EAAuBF,EAAS,aAAa,CAAC,EAE9F,OAAO,MADQ,MAAM,OAAOG,IACR,QAAQ,CAC1B,qBAAsB,CAACJ,EAAmB,qBAAuBD,EAAO,0BACxE,gBAAiBM,EACjB,WAAY,CAACE,EAAUC,IAAoB,CAOzC,GAAID,EAAS,SAAS,QAAQ,EAAG,CAC/B,IAAME,EAAMN,EAAuBF,EAASM,CAAQ,EACpD,OAAO,IAAI,gBAAgB,IAAI,KAC7B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CA6BD,EACA,CAAE,KAAQ,wBAAyB,CAAC,CAAC,CACzC,CACA,OAAO,GACT,EAIA,oBAAqBL,CACvB,CAAC,CACH,ECjDO,IAAMQ,EAAN,KAAoB,CAUzB,MAAM,eAAeC,EAAS,CAC5B,GAAM,CAAE,cAAAC,EAAe,GAAGC,CAAW,EAAIF,GAAW,CAAC,EAE/CG,EAAY,IAAIC,EAGhBC,EAAsB,IAAIC,EAChCD,EAAoB,sBAAsBF,EAAU,MAAM,EAC1D,MAAME,EAAoB,kBAAkBJ,CAAa,EAAE,MAAMM,GAAK,CAEpE,QAAQ,KAAK,0CAA2CA,CAAC,CAC3D,CAAC,EAGD,IAAMC,EAAmB,IAAIC,EAC7B,OAAAD,EAAiB,sBAAsBL,EAAU,MAAM,EAEvD,KAAK,2BACHK,EAAiB,2BAA2B,KAAKA,CAAgB,EAC5DA,EAAiB,eAAeN,CAAU,CACnD,CAgBA,MAAM,KAAK,CACT,sBAAAQ,EACA,mBAAAC,EACA,MAAAC,EACA,OAAAC,CACF,EAAI,CAAC,EAAG,CACNA,IAAW,CAAC,EAGZ,IAAMC,EAAc,SAAS,YAC7B,GAAI,CAACA,EACH,KAAM,6DAGR,IAAMC,EAAwBC,GAAa,CACzC,OAAQA,EAAU,CAChB,IAAK,SACH,OAAOC,EAAmB,2BACrBA,EAAmB,gBACnBA,EAAmB,eAC1B,QACE,MAAO,EACX,CACF,EAMMC,EAAwB,CAACC,EAAOH,IAC7BG,EAAM,UAAYH,EAGrBI,EAAqBD,GACrBA,EAAM,gBAAkB,aAAe,CAACF,EAAmB,gBAG3DJ,EAAO,UAAY,CAACK,EAAsBC,EAAON,EAAO,QAAQ,EAC3D,GAEFE,EAAqBI,EAAM,QAAQ,EAEtCA,EAAQL,EAAY,OAAO,KAAKM,CAAiB,EACvD,GAAI,CAACD,EACH,KAAM,sFAGR,IAAME,EAAO,CAAC,EACdA,EAAK,UAAY,IAAIjB,EACjBM,IACFW,EAAK,oBAAsB,IAAIf,EAC/Be,EAAK,oBAAoB,sBAAsBA,EAAK,UAAU,MAAM,EACpE,MAAMA,EAAK,oBAAoB,kBAAkBX,CAAqB,EAAE,MAAMH,GAAK,CAEjF,QAAQ,KAAK,0CAA2CA,CAAC,CAC3D,CAAC,GAGH,IAAMe,EAAmBC,EAAoBV,EAAQC,CAAW,EAC5DK,EAAM,WAAa,YACrBE,EAAK,UAAYG,EAAcH,EAAMR,EAAQI,EAAoBK,CAAgB,EACxEH,EAAM,WAAa,WAC5BE,EAAK,OAASI,EAAWJ,EAAMR,EAAQI,EAAoBK,CAAgB,GAK7E,IAAMd,EAAmB,IAAIC,EAC7B,OAAAD,EAAiB,sBAAsBa,EAAK,UAAU,MAAM,EAE5D,KAAK,2BACHb,EAAiB,2BAA2B,KAAKA,CAAgB,EAC5DA,EAAiB,KAAKW,EAAOE,EAAMR,EAAQD,EAAOD,CAAkB,CAC7E,CACF,ECpIK,OAAO,WACV,OAAO,SAAW,CAAC,GAGhB,OAAO,SAAS,SACnB,OAAO,SAAS,OAAS,IAAIe", "names": ["isBlink", "hasImageCodecs", "hasChromiumBreakIterators", "supportsWasmGC", "bytes", "browserEnvironment", "resolveUrlWithSegments", "segments", "joinPathSegments", "segment", "stripRightSlashes", "stripLeftSlashes", "x", "i", "getCanvaskitBaseUrl", "config", "buildConfig", "FlutterEntrypointLoader", "policy", "options", "entrypointUrl", "resolveUrlWithSegments", "onEntrypointLoaded", "nonce", "build", "deps", "config", "engineInitializer", "appRunner", "entryPointBaseUrl", "mainP<PERSON>", "useCallback", "scriptTag", "resolve", "reject", "entrypointBaseUrl", "mainWasmPath", "jsSupportRuntimePath", "moduleUri", "jsSupportRuntimeUri", "compiledDartAppPromise", "importsPromise", "skwasmInstance", "url", "trustedUrl", "timeout", "promise", "duration", "debugName", "timeoutId", "_clock", "_", "reject", "FlutterServiceWorkerLoader", "policy", "settings", "errorMessage", "serviceWorkerVersion", "serviceWorkerUrl", "resolveUrlWithSegments", "timeout<PERSON><PERSON><PERSON>", "url", "serviceWorkerActivation", "serviceWorkerRegistration", "newRegistration", "serviceWorker", "resolve", "FlutterTrustedTypesPolicy", "validPatterns", "policyName", "patterns", "url", "parsed", "file", "pattern", "createWasmInstantiator", "url", "modulePromise", "imports", "success<PERSON>allback", "module", "instance", "loadCanvasKit", "deps", "config", "browserEnvironment", "canvasKitBaseUrl", "supportsChromiumCanvasKit", "useChromiumCanvasKit", "baseUrl", "resolveUrlWithSegments", "canvasKitUrl", "wasmInstantiator", "createWasmInstantiator", "canvasKitModule", "loadSkwasm", "deps", "config", "browserEnvironment", "baseUrl", "rawSkwasmUrl", "resolveUrlWithSegments", "skwasmUrl", "wasmInstantiator", "createWasmInstantiator", "filename", "scriptDirectory", "url", "Flutter<PERSON><PERSON>der", "options", "serviceWorker", "entrypoint", "flutterTT", "FlutterTrustedTypesPolicy", "serviceWorkerLoader", "FlutterServiceWorkerLoader", "e", "entrypoint<PERSON><PERSON><PERSON>", "FlutterEntrypointLoader", "serviceWorkerSettings", "onEntrypointLoaded", "nonce", "config", "buildConfig", "rendererIsCompatible", "renderer", "browserEnvironment", "buildContainsRenderer", "build", "buildIsCompatible", "deps", "canvasKitBaseUrl", "getCanvaskitBaseUrl", "loadCanvasKit", "loadSkwasm", "Flutter<PERSON><PERSON>der"]}