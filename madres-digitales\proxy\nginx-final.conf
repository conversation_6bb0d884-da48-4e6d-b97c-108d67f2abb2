server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html index.htm;
    
    # Aumentar límite de tamaño de archivo para uploads
    client_max_body_size 100M;

    # Configuración de tipos MIME explícita
    location ~* \.html$ {
        add_header Content-Type "text/html; charset=utf-8" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        expires 0;
    }

    location ~* \.js$ {
        add_header Content-Type "application/javascript; charset=utf-8" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    location ~* \.css$ {
        add_header Content-Type "text/css; charset=utf-8" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    location ~* \.json$ {
        add_header Content-Type "application/json; charset=utf-8" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    # Configuración para archivos estáticos principales
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Configuración específica para la aplicación Flutter
    location = /app {
        return 301 /app/;
    }

    location /app/ {
        alias /usr/share/nginx/html/app/;

        # Archivos JavaScript específicos
        location ~* /app/.*\.js$ {
            alias /usr/share/nginx/html/app/;
            try_files $uri =404;
            add_header Content-Type "application/javascript; charset=utf-8" always;
            add_header X-Content-Type-Options "nosniff" always;
        }

        # Service Worker
        location = /app/flutter_service_worker.js {
            alias /usr/share/nginx/html/app/flutter_service_worker.js;
            add_header Content-Type "application/javascript; charset=utf-8" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        }

        # Configuración específica para el index.html de Flutter
        location = /app/ {
            alias /usr/share/nginx/html/app/;
            try_files /index.html =404;
            add_header Content-Type "text/html; charset=utf-8" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Pragma "no-cache" always;
            expires 0;
        }

        # Para otros archivos estáticos
        try_files $uri $uri/ @app_fallback;
    }

    # Fallback para la app Flutter (solo para rutas HTML)
    location @app_fallback {
        alias /usr/share/nginx/html/app/;
        try_files /index.html =404;
        add_header Content-Type "text/html; charset=utf-8" always;
    }

    # Configuración para la API
    location /api/ {
        proxy_pass http://backend:4001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Configuraciones adicionales para mejorar la estabilidad
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        send_timeout 300s;
        
        # Deshabilitar caché para la API
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        expires 0;
    }

    # Dashboard personalizado
    location /dashboard/ {
        try_files /dashboard.html =404;
    }
    
    location = /dashboard {
        return 301 /dashboard/;
    }

    # Metabase (accesible en /dashboard-metabase/)
    location /dashboard-metabase/ {
        proxy_pass http://metabase:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Páginas de administración
    location /admin-contenido.html {
        try_files /admin-contenido.html =404;
    }
    
    location /admin-contenido-fixed.html {
        try_files /admin-contenido-fixed.html =404;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}