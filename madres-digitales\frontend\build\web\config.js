// Configuración específica para web
window.flutterConfiguration = {
  canvasKitBaseUrl: "/canvaskit/",
  apiBaseUrl: "/api"
};

// Interceptar cualquier request a localhost:4001 y redirigir a /api
if (typeof window !== 'undefined') {
  // Override fetch para interceptar requests
  const originalFetch = window.fetch;
  window.fetch = function(url, options) {
    if (typeof url === 'string' && url.includes('localhost:4001')) {
      console.log('🔄 Interceptando request a localhost:4001, redirigiendo a /api');
      url = url.replace('http://localhost:4001', '').replace('localhost:4001', '');
      if (!url.startsWith('/api')) {
        url = '/api' + (url.startsWith('/') ? url : '/' + url);
      }
      console.log('🔄 Nueva URL:', url);
    }
    return originalFetch.call(this, url, options);
  };
}