{{ >head }}

  <div
      id="dartdoc-main-content"
      class="main-content"
      data-above-sidebar="{{ aboveSidebarPath }}"
      data-below-sidebar="{{ belowSidebarPath }}">
    {{ #self }}
      <div>{{ >source_link }}<h1><span class="kind-top-level-property">{{{ name }}}</span> {{ kind }} {{ >feature_set }} {{ >categorization }}</h1></div>

      {{ ^hasGetterOrSetter }}
        <section class="multi-line-signature">
          {{ >annotations }}
          {{{ modelType.linkedName }}}
          {{ >name_summary }}
          {{ >attributes }}
        </section>
        {{ >documentation }}
        {{ >source_code }}
      {{ /hasGetterOrSetter }}

      {{ #hasExplicitGetter }}
        {{ >accessor_getter }}
      {{ /hasExplicitGetter }}

      {{ #hasExplicitSetter }}
        {{ >accessor_setter }}
      {{ /hasExplicitSetter }}
    {{ /self }}
  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-left" class="sidebar sidebar-offcanvas-left">
    {{ >search_sidebar }}
    <h5>{{ parent.name }} {{ parent.kind }}</h5>
    <div id="dartdoc-sidebar-left-content"></div>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-sidebar-right" class="sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

{{ >footer }}
